export class WebGLRenderer {
  private gl: WebGLRenderingContext;
  private program: WebGLProgram;
  private positionLocation: number;
  private texcoordLocation: number;
  private yTexture: WebGLTexture;
  private uTexture: WebGLTexture;
  private vTexture: WebGLTexture;

  constructor(private canvas: HTMLCanvasElement) {
    const gl = this.canvas.getContext('webgl');
    if (!gl) {
      throw new Error('WebGL not supported');
    }
    this.gl = gl;

    const vs = `
      attribute vec4 a_position;
      attribute vec2 a_texcoord;
      varying vec2 v_texcoord;
      void main() {
        gl_Position = a_position;
        v_texcoord = a_texcoord;
      }
    `;

    const fs = `
      precision mediump float;
      varying vec2 v_texcoord;
      uniform sampler2D u_y_texture;
      uniform sampler2D u_u_texture;
      uniform sampler2D u_v_texture;
      void main() {
        // Sample YUV values
        float y = texture2D(u_y_texture, v_texcoord).r;
        float u = texture2D(u_u_texture, v_texcoord).r;
        float v = texture2D(u_v_texture, v_texcoord).r;

        // Proper limited range YUV to RGB conversion
        // Convert texture values [0,1] back to [0,255] range
        y = y * 255.0;
        u = u * 255.0;
        v = v * 255.0;

        // Convert from limited range to full range
        // Y: 16-235 -> 0-255, U/V: 16-240 -> 0-255
        y = clamp((y - 16.0) * 255.0 / 219.0, 0.0, 255.0);
        u = clamp((u - 16.0) * 255.0 / 224.0, 0.0, 255.0);
        v = clamp((v - 16.0) * 255.0 / 224.0, 0.0, 255.0);

        // Center U and V around 128 in full range
        u = u - 128.0;
        v = v - 128.0;

        // BT.601 YUV to RGB conversion
        float r = y + 1.402 * v;
        float g = y - 0.344 * u - 0.714 * v;
        float b = y + 1.772 * u;

        // Normalize to [0,1] and clamp
        r = clamp(r / 255.0, 0.0, 1.0);
        g = clamp(g / 255.0, 0.0, 1.0);
        b = clamp(b / 255.0, 0.0, 1.0);

        // Debug: try showing just the Y channel as grayscale
        // gl_FragColor = vec4(y, y, y, 1.0);

        gl_FragColor = vec4(clamp(r, 0.0, 1.0), clamp(g, 0.0, 1.0), clamp(b, 0.0, 1.0), 1.0);
      }
    `;

    this.program = this.createProgram(vs, fs);
    this.positionLocation = gl.getAttribLocation(this.program, 'a_position');
    this.texcoordLocation = gl.getAttribLocation(this.program, 'a_texcoord');

    this.yTexture = this.createTexture();
    this.uTexture = this.createTexture();
    this.vTexture = this.createTexture();
  }

  public render(frame: any) {
    const gl = this.gl;
    gl.useProgram(this.program);

    const positionBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([-1, -1, 1, -1, -1, 1, 1, 1]), gl.STATIC_DRAW);
    gl.enableVertexAttribArray(this.positionLocation);
    gl.vertexAttribPointer(this.positionLocation, 2, gl.FLOAT, false, 0, 0);

    const texcoordBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, texcoordBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array([0, 1, 1, 1, 0, 0, 1, 0]), gl.STATIC_DRAW);
    gl.enableVertexAttribArray(this.texcoordLocation);
    gl.vertexAttribPointer(this.texcoordLocation, 2, gl.FLOAT, false, 0, 0);

    this.uploadTexture(this.yTexture, frame.y);
    this.uploadTexture(this.uTexture, frame.u);
    this.uploadTexture(this.vTexture, frame.v);

    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
  }

  private createProgram(vs: string, fs: string): WebGLProgram {
    const gl = this.gl;
    const program = gl.createProgram();
    if (!program) {
      throw new Error('Failed to create program');
    }
    const vertexShader = this.createShader(gl.VERTEX_SHADER, vs);
    const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, fs);
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);
    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      throw new Error(`Program link error: ${gl.getProgramInfoLog(program)}`);
    }
    return program;
  }

  private createShader(type: number, source: string): WebGLShader {
    const gl = this.gl;
    const shader = gl.createShader(type);
    if (!shader) {
      throw new Error('Failed to create shader');
    }
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      throw new Error(`Shader compile error: ${gl.getShaderInfoLog(shader)}`);
    }
    return shader;
  }

  private createTexture(): WebGLTexture {
    const gl = this.gl;
    const texture = gl.createTexture();
    if (!texture) {
      throw new Error('Failed to create texture');
    }
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.NEAREST);
    gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.NEAREST);
    return texture;
  }

  private uploadTexture(texture: WebGLTexture, data: any) {
    const gl = this.gl;
    gl.bindTexture(gl.TEXTURE_2D, texture);
    gl.texImage2D(gl.TEXTURE_2D, 0, gl.LUMINANCE, data.width, data.height, 0, gl.LUMINANCE, gl.UNSIGNED_BYTE, data.data);
  }
}