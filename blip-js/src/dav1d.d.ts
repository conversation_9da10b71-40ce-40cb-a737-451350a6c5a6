declare module 'dav1d.js' {
  export interface Dav1dDecoder {
    decodeFrameAsYUV(obu: Uint8Array): any;
    decodeFrameAsBMP(obu: Uint8Array): any;
    unsafeDecodeFrameAsYUV(obu: Uint8Array): any;
    unsafeDecodeFrameAsBMP(obu: Uint8Array): any;
    unsafeCleanup(): void;
  }

  export function create(opts?: { wasmURL?: string; wasmData?: ArrayBuffer }): Promise<Dav1dDecoder>;

  const Dav1dModule: {
    create: typeof create;
  };

  export default Dav1dModule;
}