import { BlipParser, BlipHeader } from './blip-parser';
import * as MP4Box from 'mp4box';
import Dav1dModule from 'dav1d.js';
import { WebGLRenderer } from './webgl-renderer';

export class BlipPlayer {
  private canvas: HTMLCanvasElement;
  private parser!: BlipParser;
  private header!: BlipHeader;
  private videoPayload!: Uint8Array;
  private video!: HTMLVideoElement;
  private mediaSource!: MediaSource;
  private sourceBuffer!: SourceBuffer;
  private isPlaying = false;
  private decoder!: any;
  private renderer!: WebGLRenderer;

  constructor(private src: string, canvas: HTMLCanvasElement) {
    this.canvas = canvas;
  }

  public async load() {
    this.parser = await BlipParser.fromUrl(this.src);
    if (!this.parser.validateCrc32()) {
      throw new Error('CRC32 checksum mismatch');
    }
    this.header = this.parser.parseHeader();
    this.videoPayload = this.parser.getVideoPayload();
    this.canvas.width = this.header.width;
    this.canvas.height = this.header.height;
  }

  public play() {
    console.log('play called');
    if (this.isPlaying) return;
    this.isPlaying = true;

    // For now, let's try WASM decoding first to see if it works
    console.log('playing wasm');
    this.playWasm();

    // TODO: Fix native AV1 playback later
    // if (BlipPlayer.isAv1Supported()) {
    //   console.log('playing native');
    //   this.playNative();
    // } else {
    //   console.log('playing wasm');
    //   this.playWasm();
    // }
  }

  public pause() {
    if (!this.isPlaying) return;
    this.isPlaying = false;
    if (this.video) {
      this.video.pause();
    }
  }

  public destroy() {
    this.pause();
    if (this.video) {
      URL.revokeObjectURL(this.video.src);
    }
    if (this.decoder) {
      this.decoder.close();
    }
  }

  private playNative() {
    console.log('playNative called');
    this.video = document.createElement('video');
    this.mediaSource = new MediaSource();
    this.video.src = URL.createObjectURL(this.mediaSource);
    console.log('video src:', this.video.src);
    this.video.loop = true;
    this.video.muted = true; // Required for autoplay in many browsers
    this.video.style.display = 'none'; // Hide the video element since we're rendering to canvas
    document.body.appendChild(this.video); // Add to DOM for proper functionality

    this.video.addEventListener('error', (e) => {
      console.error('Video error:', e);
    });

    this.mediaSource.addEventListener('sourceopen', () => {
      console.log('sourceopen');
      const mime = 'video/mp4; codecs="av01.0.05M.08"';
      console.log('mime:', mime);
      this.sourceBuffer = this.mediaSource.addSourceBuffer(mime);

      const mp4boxfile: any = MP4Box.createFile();
      console.log('mp4boxfile:', mp4boxfile);
      mp4boxfile.onReady = (info: any) => {
        console.log('moov ready:', info);
        console.log('moov buffer:', info.moov);
        try {
          this.sourceBuffer.appendBuffer(info.moov);
        } catch (error) {
          console.error('Error appending moov buffer:', error);
        }
      };

      this.sourceBuffer.addEventListener('updateend', () => {
        console.log('source buffer update end');
        if (!this.sourceBuffer.updating) {
          mp4boxfile.flush();
        }
      });

      this.sourceBuffer.addEventListener('error', (e) => {
        console.error('SourceBuffer error:', e);
      });

      const trackOptions = {
        timescale: 1000,
        width: this.header.width,
        height: this.header.height,
        nb_samples: this.header.frameCount,
        codec: 'av01.0.05M.08',
        description: this.createAv1cBox()
      };

      const trackId = mp4boxfile.addTrack(trackOptions);
      const sampleOptions = {
        duration: 1000 / this.header.framerate,
        is_sync: true
      };
      const videoBuffer = this.videoPayload.buffer;
      mp4boxfile.addSample(trackId, videoBuffer, sampleOptions);

      // Finalize the MP4 file to trigger onReady
      mp4boxfile.flush();
    });

    this.video.play();
    this.renderLoop();
  }

  private async playWasm() {
    this.renderer = new WebGLRenderer(this.canvas);
    try {
      // Create the dav1d decoder with WASM URL
      this.decoder = await Dav1dModule.create({
        wasmURL: '/node_modules/dav1d.js/dav1d.wasm'
      });

      console.log('WASM decoder created successfully');

      // Parse IVF file to extract AV1 OBUs
      const fullPayload = this.getFullVideoPayload();
      const frames = this.parseIVFFrames(fullPayload);
      console.log('Extracted', frames.length, 'frames from IVF');

      if (frames.length === 0) {
        console.error('No frames found in IVF file');
        return;
      }

      // Start animation loop with all frames
      this.playFrameSequence(frames);

    } catch (error) {
      console.error('WASM decoding failed:', error);
    }
  }

  private async playFrameSequence(frames: Uint8Array[]) {
    try {
      // Loop the animation continuously
      while (this.isPlaying) {
        for (let i = 0; i < frames.length && this.isPlaying; i++) {
        const frame = frames[i];
        console.log(`Decoding frame ${i + 1}/${frames.length}, size: ${frame.length}`);

        try {
          // Use unsafe decode and immediately copy the data to avoid buffer reuse
          const unsafeData = this.decoder.unsafeDecodeFrameAsYUV(frame);

          if (unsafeData) {
            // Immediately copy the unsafe data to our own buffer
            const safeCopy = new Uint8Array(unsafeData.length);
            safeCopy.set(unsafeData);

            // Clean up the unsafe reference immediately
            this.decoder.unsafeCleanup();

            // Create a frame object with our safe copy
            const decodedFrame = {
              width: this.header.width,
              height: this.header.height,
              data: safeCopy
            };

            // Debug: Check YUV plane parsing for first frame
            if (i === 0) {
              console.log(`Decoding ${frames.length} frames at ${decodedFrame.width}x${decodedFrame.height}`);
              console.log('Total data length:', decodedFrame.data.length);

              // Check our YUV plane calculations
              const ySize = decodedFrame.width * decodedFrame.height;
              const uvSize = (decodedFrame.width / 2) * (decodedFrame.height / 2);
              console.log('Expected Y size:', ySize, 'U size:', uvSize, 'V size:', uvSize);
              console.log('Expected total:', ySize + uvSize + uvSize);

              // Sample some values from each plane
              const yData = decodedFrame.data.slice(0, ySize);
              const uData = decodedFrame.data.slice(ySize, ySize + uvSize);
              const vData = decodedFrame.data.slice(ySize + uvSize, ySize + uvSize + uvSize);

              console.log('Y plane samples (first 10):', Array.from(yData.slice(0, 10)));
              console.log('U plane samples (first 10):', Array.from(uData.slice(0, 10)));
              console.log('V plane samples (first 10):', Array.from(vData.slice(0, 10)));
            }

            // Convert YUV420 planar data to separate Y, U, V planes
            const yuvFrame = this.parseYUV420Frame(decodedFrame);
            this.renderer.render(yuvFrame);
          } else {
            console.warn(`Frame ${i + 1} decode returned null`);
          }
        } catch (frameError) {
          console.error(`Error decoding frame ${i + 1}:`, frameError);
          // Continue with next frame instead of stopping
          continue;
        }

          // Wait for the appropriate frame duration
          const frameDuration = 1000 / this.header.framerate;
          await new Promise(resolve => setTimeout(resolve, frameDuration));
        }
        console.log('Animation loop completed, restarting...');
      }

      console.log('WASM animation stopped');
    } catch (error) {
      console.error('Frame sequence playback failed:', error);
    }
  }

  private getFullVideoPayload(): Uint8Array {
    const headerSize = 13;
    const crc32Size = 4;
    // Return the video payload including IVF headers
    const payloadStart = headerSize;
    const payloadLength = this.parser['dataView'].byteLength - headerSize - crc32Size;
    return new Uint8Array(this.parser['dataView'].buffer, payloadStart, payloadLength);
  }

  private parseIVFFrames(ivfData: Uint8Array): Uint8Array[] {
    const frames: Uint8Array[] = [];
    const dataView = new DataView(ivfData.buffer, ivfData.byteOffset, ivfData.byteLength);

    // Skip IVF file header (32 bytes)
    // DKIF signature (4) + version (2) + header length (2) + fourcc (4) +
    // width (2) + height (2) + timebase (8) + frame count (4) + unused (4)
    let offset = 32;

    while (offset < ivfData.length) {
      if (offset + 12 > ivfData.length) break; // Not enough data for frame header

      // Read IVF frame header (12 bytes)
      const frameSize = dataView.getUint32(offset, true); // little endian
      const timestamp = dataView.getBigUint64(offset + 4, true); // little endian
      offset += 12;

      if (frameSize === 0 || offset + frameSize > ivfData.length) {
        console.warn('Invalid frame size or not enough data:', frameSize, 'at offset', offset);
        break;
      }

      // Extract the AV1 OBU data (without IVF frame header)
      const frameData = new Uint8Array(ivfData.buffer, ivfData.byteOffset + offset, frameSize);
      frames.push(frameData);

      console.log(`Frame ${frames.length}: size=${frameSize}, timestamp=${timestamp}, offset=${offset}`);

      offset += frameSize;
    }

    return frames;
  }

  private parseYUV420Frame(frame: { width: number; height: number; data: Uint8Array }) {
    const { width, height, data } = frame;

    // YUV420 planar format:
    // Y plane: width × height bytes
    // U plane: (width/2) × (height/2) bytes
    // V plane: (width/2) × (height/2) bytes

    const ySize = width * height;
    const uvSize = (width / 2) * (height / 2);

    const yData = data.slice(0, ySize);
    const vData = data.slice(ySize, ySize + uvSize);  // Try swapping U and V
    const uData = data.slice(ySize + uvSize, ySize + uvSize + uvSize);



    return {
      y: {
        width: width,
        height: height,
        data: yData
      },
      u: {
        width: width / 2,
        height: height / 2,
        data: uData
      },
      v: {
        width: width / 2,
        height: height / 2,
        data: vData
      }
    };
  }

  private renderLoop() {
    if (!this.isPlaying) return;
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(this.video, 0, 0, this.header.width, this.header.height);
    }
    requestAnimationFrame(() => this.renderLoop());
  }

  public static isAv1Supported(): boolean {
    return 'MediaSource' in window && MediaSource.isTypeSupported('video/mp4; codecs="av01.0.05M.08"');
  }

  private createAv1cBox(): Uint8Array {
    // See ISO/IEC 14496-15:2019, Section 8.3.3.2
    const av1c = new Uint8Array([
      0x81, // marker (1), version (7)
      0x00, // seq_profile (3), seq_level_idx_0 (5)
      0x00, // seq_tier_0 (1), high_bitdepth (1), twelve_bit (1), monochrome (1), chroma_subsampling_x (1), chroma_subsampling_y (1), chroma_sample_position (2)
      0x00, // reserved (3), initial_presentation_delay_present (1), initial_presentation_delay_minus_one (4)
    ]);
    // seq_profile = 0 (Main), seq_level_idx_0 = 13 (5.1)
    av1c[1] = (0 << 5) | 13;
    return av1c;
  }
}