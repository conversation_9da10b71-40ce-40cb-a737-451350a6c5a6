<!DOCTYPE html>
<html>
<head>
  <title>blip-js example</title>
  <style>
    canvas {
      border: 1px solid #ccc;
      display: block;
      margin: 10px 0;
    }
    button {
      padding: 10px 20px;
      margin: 10px 0;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <h1>blip-js example</h1>

  <h2>Using the class-based API</h2>
  <canvas id="my-canvas"></canvas>
  <button id="play-button">Play</button>

  <h2>Using the custom HTML element</h2>
  <blip-player src="../test.blip"></blip-player>

  <script src="../dist/blip.umd.js?v=34"></script>
  <script>
    // Check AV1 support
    console.log('AV1 supported:', Blip.BlipPlayer.isAv1Supported());
    console.log('MediaSource supported:', 'MediaSource' in window);
    if ('MediaSource' in window) {
      console.log('AV1 codec supported:', MediaSource.isTypeSupported('video/mp4; codecs="av01.0.05M.08"'));
    }

    const canvas = document.getElementById('my-canvas');
    const player = new Blip.BlipPlayer('../test.blip', canvas);

    // Load the blip file first, then enable the play button
    player.load().then(() => {
      console.log('Blip file loaded successfully');
      const playButton = document.getElementById('play-button');
      playButton.addEventListener('click', () => {
        player.play();
      });
      playButton.disabled = false;
      playButton.textContent = 'Play';
    }).catch(error => {
      console.error('Failed to load blip file:', error);
      const playButton = document.getElementById('play-button');
      playButton.textContent = 'Error loading file';
    });

    // Disable play button initially
    const playButton = document.getElementById('play-button');
    playButton.disabled = true;
    playButton.textContent = 'Loading...';
  </script>
</body>
</html>