<!DOCTYPE html>
<html>
<head>
  <title>Color Comparison</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    .comparison {
      display: flex;
      gap: 20px;
      align-items: flex-start;
    }
    .video-container {
      text-align: center;
    }
    video, canvas {
      border: 1px solid #ccc;
      display: block;
      margin: 10px 0;
    }
    button {
      padding: 10px 20px;
      margin: 10px 0;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <h1>Color Comparison: MP4 vs BLIP</h1>
  
  <div class="comparison">
    <div class="video-container">
      <h2>Original MP4</h2>
      <video id="mp4-video" width="128" height="72" controls loop autoplay muted>
        <source src="../test.mp4" type="video/mp4">
        Your browser does not support the video tag.
      </video>
    </div>
    
    <div class="video-container">
      <h2>BLIP Player</h2>
      <canvas id="blip-canvas" width="128" height="72"></canvas>
      <button id="play-button">Play BLIP</button>
    </div>
  </div>

  <script src="../dist/blip.umd.js?v=35"></script>
  <script>
    const canvas = document.getElementById('blip-canvas');
    const player = new Blip.BlipPlayer('../test.blip', canvas);

    // Load the blip file first, then enable the play button
    player.load().then(() => {
      console.log('Blip file loaded successfully');
      const playButton = document.getElementById('play-button');
      playButton.addEventListener('click', () => {
        player.play();
      });
      playButton.disabled = false;
      playButton.textContent = 'Play BLIP';
    }).catch(error => {
      console.error('Failed to load blip file:', error);
      const playButton = document.getElementById('play-button');
      playButton.textContent = 'Error loading file';
    });

    // Disable play button initially
    const playButton = document.getElementById('play-button');
    playButton.disabled = true;
    playButton.textContent = 'Loading...';
  </script>
</body>
</html>
